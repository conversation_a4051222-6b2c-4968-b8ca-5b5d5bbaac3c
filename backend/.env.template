HUGGINGFACE_TOKEN=
BUCKET_SPEECH_PROFILES=
BUCKET_BACKUPS=
BUCKET_PLUGINS_LOGOS=

GOOGLE_APPLICATION_CREDENTIALS=google-credentials.json

# Firebase Service Account J<PERSON><PERSON> (for notifications)
# Copy your Firebase service account JSO<PERSON> here as a single line
SERVICE_ACCOUNT_JSON=

PINECONE_API_KEY=
PINECONE_INDEX_NAME=

REDIS_DB_HOST=
REDIS_DB_PORT=
REDIS_DB_PASSWORD=

SONIOX_API_KEY=
DEEPGRAM_API_KEY=

ADMIN_KEY=
OPENAI_API_KEY=

GITHUB_TOKEN=

WORKFLOW_API_KEY=
HUME_API_KEY=
HUME_CALLBACK_URL=

HOSTED_PUSHER_API_URL=

TYPESENSE_HOST=
TYPESENSE_HOST_PORT=
TYPESENSE_API_KEY=

STRIPE_API_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_CONNECT_WEBHOOK_SECRET=

BASE_API_URL=

RAPID_API_HOST=
RAPID_API_KEY=

# Firebase OAuth
FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=

# Encrypt the conversations, memories, chat messages
ENCRYPTION_SECRET='omi_ZwB2ZNqB2HHpMK6wStk7sTpavJiPTFg7gXUHnc4tFABPU6pZ2c2DKgehtfgi4RZv'
