#!/usr/bin/env python3
"""
OMI Notification Service Daemon

This service runs continuously and checks for notifications every minute.
It's designed to be run as a systemd service for better process management.

Usage:
    python3 notification_service.py

Environment Variables Required:
    - SERVICE_ACCOUNT_JSON: Firebase service account JSON
    - Or ensure Firebase is properly initialized in your environment
"""

import asyncio
import json
import os
import sys
import logging
import signal
from datetime import datetime
from pathlib import Path
import time

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass  # python-dotenv not available, use system environment variables

import firebase_admin
import pytz

# Import the notification logic
from utils.other.notifications import start_cron_job


class NotificationService:
    def __init__(self):
        self.running = True
        self.logger = self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('/tmp/omi_notification_service.log', mode='a')
            ]
        )
        return logging.getLogger('OMI-NotificationService')
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}. Shutting down gracefully...")
        self.running = False
    
    def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase is already initialized
            firebase_admin.get_app()
            self.logger.info("Firebase already initialized")
            return True
        except ValueError:
            # Firebase not initialized, initialize it
            pass
        
        try:
            if os.environ.get('SERVICE_ACCOUNT_JSON'):
                service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
                credentials = firebase_admin.credentials.Certificate(service_account_info)
                firebase_admin.initialize_app(credentials)
                self.logger.info("Firebase initialized with service account JSON")
                return True
            else:
                # Try to initialize with default credentials
                firebase_admin.initialize_app()
                self.logger.info("Firebase initialized with default credentials")
                return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Firebase: {e}")
            return False
    
    async def run_notification_check(self):
        """Run a single notification check"""
        try:
            self.logger.debug("Running notification check...")
            await start_cron_job()
            self.logger.debug("Notification check completed")
        except Exception as e:
            self.logger.error(f"Error in notification check: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    async def run_service(self):
        """Main service loop"""
        self.logger.info("Starting OMI Notification Service")
        self.logger.info(f"Service started at {datetime.now(pytz.utc)}")
        
        # Initialize Firebase
        if not self.initialize_firebase():
            self.logger.error("Failed to initialize Firebase. Exiting.")
            return False
        
        # Main service loop
        while self.running:
            try:
                # Run notification check
                await self.run_notification_check()
                
                # Wait for 60 seconds before next check
                for _ in range(60):
                    if not self.running:
                        break
                    await asyncio.sleep(1)
                    
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt. Shutting down...")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error in service loop: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                # Wait a bit before retrying
                await asyncio.sleep(30)
        
        self.logger.info("OMI Notification Service stopped")
        return True


def main():
    """Main function"""
    service = NotificationService()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGTERM, service.signal_handler)
    signal.signal(signal.SIGINT, service.signal_handler)
    
    try:
        # Run the service
        asyncio.run(service.run_service())
    except KeyboardInterrupt:
        service.logger.info("Service interrupted by user")
    except Exception as e:
        service.logger.error(f"Service failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
