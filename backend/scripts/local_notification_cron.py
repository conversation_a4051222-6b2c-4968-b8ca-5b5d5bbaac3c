#!/usr/bin/env python3
"""
Local Notification Cron Job Script

This script runs the notification system locally using the same business logic
as defined in utils/other/notifications.py. It should be scheduled to run
every minute via cron to check if notifications need to be sent.

Usage:
    python3 local_notification_cron.py

Environment Variables Required:
    - SERVICE_ACCOUNT_JSON: Firebase service account JSON
    - Or ensure Firebase is properly initialized in your environment

Cron Schedule:
    * * * * * cd /path/to/backend && python3 scripts/local_notification_cron.py >> /var/log/omi_notifications.log 2>&1
"""

import asyncio
import json
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass  # python-dotenv not available, use system environment variables

import firebase_admin
import pytz

# Import the notification logic
from utils.other.notifications import start_cron_job


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('/tmp/omi_notifications.log', mode='a')
        ]
    )
    return logging.getLogger(__name__)


def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if Firebase is already initialized
        firebase_admin.get_app()
        return True
    except ValueError:
        # Firebase not initialized, initialize it
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            return True
        else:
            # Try to initialize with default credentials
            firebase_admin.initialize_app()
            return True
    except Exception as e:
        logging.error(f"Failed to initialize Firebase: {e}")
        return False


async def main():
    """Main function to run the notification cron job"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("Starting Local Notification Cron Job")
    logger.info(f"Current UTC time: {datetime.now(pytz.utc)}")
    
    # Initialize Firebase
    if not initialize_firebase():
        logger.error("Failed to initialize Firebase. Exiting.")
        sys.exit(1)
    
    try:
        # Run the notification cron job logic
        await start_cron_job()
        logger.info("Notification cron job completed successfully")
    except Exception as e:
        logger.error(f"Error running notification cron job: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
    
    logger.info("Local Notification Cron Job finished")
    logger.info("=" * 60)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
