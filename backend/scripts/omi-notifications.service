[Unit]
Description=OMI Notification Service
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/omi/backend
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/omi/backend
ExecStart=/usr/bin/python3 /home/<USER>/omi/backend/scripts/notification_service.py
Restart=always
RestartSec=60
StandardOutput=journal
StandardError=journal

# Environment file (optional - create this file with your environment variables)
EnvironmentFile=-/home/<USER>/omi/backend/.env

[Install]
WantedBy=multi-user.target
