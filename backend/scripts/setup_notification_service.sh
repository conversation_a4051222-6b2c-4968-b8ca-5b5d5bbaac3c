#!/bin/bash

# Setup script for OMI Notification Service (systemd)
# This script sets up a systemd service for continuous notification monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="omi-notifications"
BACKEND_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SERVICE_FILE="$BACKEND_DIR/scripts/omi-notifications.service"
SYSTEMD_DIR="/etc/systemd/system"
SERVICE_PATH="$SYSTEMD_DIR/$SERVICE_NAME.service"

echo -e "${BLUE}=== OMI Notification Service Setup ===${NC}"
echo -e "Backend directory: ${BACKEND_DIR}"
echo -e "Service name: ${SERVICE_NAME}"
echo ""

# Check if running as root for systemd operations
check_sudo() {
    if [ "$EUID" -ne 0 ] && [ "$1" != "status" ] && [ "$1" != "logs" ]; then
        echo -e "${YELLOW}This script needs sudo privileges for systemd operations.${NC}"
        echo -e "${YELLOW}Re-running with sudo...${NC}"
        exec sudo "$0" "$@"
    fi
}

# Update service file with correct paths
update_service_file() {
    local temp_service="/tmp/$SERVICE_NAME.service"
    
    # Create updated service file with correct paths
    cat > "$temp_service" << EOF
[Unit]
Description=OMI Notification Service
After=network.target

[Service]
Type=simple
User=$SUDO_USER
Group=$SUDO_USER
WorkingDirectory=$BACKEND_DIR
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=$BACKEND_DIR
ExecStart=/usr/bin/python3 $BACKEND_DIR/scripts/notification_service.py
Restart=always
RestartSec=60
StandardOutput=journal
StandardError=journal

# Environment file (optional - create this file with your environment variables)
EnvironmentFile=-$BACKEND_DIR/.env

[Install]
WantedBy=multi-user.target
EOF
    
    echo "$temp_service"
}

# Install the service
install_service() {
    echo -e "${YELLOW}Installing OMI Notification Service...${NC}"
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}Error: python3 is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Check if the notification service script exists
    if [ ! -f "$BACKEND_DIR/scripts/notification_service.py" ]; then
        echo -e "${RED}Error: notification_service.py not found${NC}"
        exit 1
    fi
    
    # Make scripts executable
    chmod +x "$BACKEND_DIR/scripts/notification_service.py"
    chmod +x "$BACKEND_DIR/scripts/local_notification_cron.py"
    
    # Create updated service file
    local temp_service=$(update_service_file)
    
    # Copy service file to systemd directory
    cp "$temp_service" "$SERVICE_PATH"
    rm "$temp_service"
    
    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    echo -e "${GREEN}✓ Service installed and enabled${NC}"
}

# Start the service
start_service() {
    echo -e "${YELLOW}Starting OMI Notification Service...${NC}"
    systemctl start "$SERVICE_NAME"
    echo -e "${GREEN}✓ Service started${NC}"
}

# Stop the service
stop_service() {
    echo -e "${YELLOW}Stopping OMI Notification Service...${NC}"
    systemctl stop "$SERVICE_NAME"
    echo -e "${GREEN}✓ Service stopped${NC}"
}

# Restart the service
restart_service() {
    echo -e "${YELLOW}Restarting OMI Notification Service...${NC}"
    systemctl restart "$SERVICE_NAME"
    echo -e "${GREEN}✓ Service restarted${NC}"
}

# Show service status
show_status() {
    echo -e "${BLUE}=== Service Status ===${NC}"
    systemctl status "$SERVICE_NAME" --no-pager || true
    echo ""
    
    echo -e "${BLUE}=== Recent Logs ===${NC}"
    journalctl -u "$SERVICE_NAME" --no-pager -n 10 || true
}

# Show logs
show_logs() {
    echo -e "${BLUE}=== Service Logs ===${NC}"
    journalctl -u "$SERVICE_NAME" -f
}

# Remove the service
remove_service() {
    echo -e "${YELLOW}Removing OMI Notification Service...${NC}"
    
    # Stop and disable service
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # Remove service file
    rm -f "$SERVICE_PATH"
    
    # Reload systemd
    systemctl daemon-reload
    
    echo -e "${GREEN}✓ Service removed${NC}"
}

# Test the notification script
test_script() {
    echo -e "${YELLOW}Testing notification script...${NC}"
    cd "$BACKEND_DIR"
    
    if python3 scripts/local_notification_cron.py; then
        echo -e "${GREEN}✓ Notification script test completed${NC}"
    else
        echo -e "${RED}✗ Notification script test failed${NC}"
        echo -e "${YELLOW}Please check your Firebase configuration and environment variables${NC}"
        return 1
    fi
}

# Main command handling
case "${1:-menu}" in
    "install")
        check_sudo "$@"
        install_service
        ;;
    "start")
        check_sudo "$@"
        start_service
        ;;
    "stop")
        check_sudo "$@"
        stop_service
        ;;
    "restart")
        check_sudo "$@"
        restart_service
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "remove")
        check_sudo "$@"
        remove_service
        ;;
    "test")
        test_script
        ;;
    "menu"|*)
        echo -e "${BLUE}Available commands:${NC}"
        echo -e "  ${GREEN}install${NC}  - Install the systemd service"
        echo -e "  ${GREEN}start${NC}    - Start the service"
        echo -e "  ${GREEN}stop${NC}     - Stop the service"
        echo -e "  ${GREEN}restart${NC}  - Restart the service"
        echo -e "  ${GREEN}status${NC}   - Show service status"
        echo -e "  ${GREEN}logs${NC}     - Show service logs (follow mode)"
        echo -e "  ${GREEN}remove${NC}   - Remove the service"
        echo -e "  ${GREEN}test${NC}     - Test the notification script"
        echo ""
        echo -e "Usage: $0 [install|start|stop|restart|status|logs|remove|test]"
        echo ""
        echo -e "${YELLOW}Quick setup:${NC}"
        echo -e "1. Set up your Firebase credentials (SERVICE_ACCOUNT_JSON environment variable)"
        echo -e "2. Run: $0 test"
        echo -e "3. Run: $0 install"
        echo -e "4. Run: $0 start"
        echo -e "5. Monitor: $0 logs"
        ;;
esac
