# OMI Notifications - Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Test the System
```bash
cd /home/<USER>/omi/backend
python3 scripts/test_notifications.py
```

### 2. Configure Firebase (if needed)
If the test fails, set up your Firebase credentials:
```bash
# Copy environment template
cp .env.template .env

# Edit .env and add your Firebase service account JSON
nano .env
```

Add this line to your `.env` file:
```bash
SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"your-project-id",...}
```

### 3. Choose Your Setup Method

#### Option A: Systemd Service (Recommended)
```bash
# Install and start the service
./scripts/setup_notification_service.sh install
./scripts/setup_notification_service.sh start

# Monitor logs
./scripts/setup_notification_service.sh logs
```

#### Option B: Cron Job (Simple)
```bash
# Install cron job
./scripts/setup_notification_cron.sh install

# Check status
./scripts/setup_notification_cron.sh status
```

## 📋 What This Does

- **Morning (8:00 AM local time)**: Sends "Wear your Memorion device..." notifications
- **Evening (10:00 PM local time)**: Sends daily conversation summaries
- **Automatic**: Runs every minute, checks all global timezones
- **Smart**: Only sends to users at the right local time

## 🔧 Management Commands

### Systemd Service
```bash
./scripts/setup_notification_service.sh [install|start|stop|restart|status|logs|remove]
```

### Cron Job
```bash
./scripts/setup_notification_cron.sh [install|test|status|remove]
```

## 📊 Monitoring

### Check if it's working:
```bash
# For systemd service
./scripts/setup_notification_service.sh status

# For cron job
./scripts/setup_notification_cron.sh status
```

### View logs:
```bash
# Systemd service logs
./scripts/setup_notification_service.sh logs

# Cron job logs
tail -f /var/log/omi_notifications.log
```

## 🆘 Troubleshooting

### Test manually:
```bash
python3 scripts/test_notifications.py
```

### Common issues:
1. **Firebase error**: Check your `SERVICE_ACCOUNT_JSON` in `.env`
2. **Import error**: Make sure you're in `/home/<USER>/omi/backend`
3. **Permission error**: Run setup scripts with proper permissions

### Get help:
```bash
# Show all available commands
./scripts/setup_notification_service.sh
./scripts/setup_notification_cron.sh
```

## 📁 Files Created

- `scripts/local_notification_cron.py` - Main cron script
- `scripts/notification_service.py` - Service daemon
- `scripts/test_notifications.py` - Test suite
- `scripts/setup_*.sh` - Setup scripts
- `scripts/README_NOTIFICATIONS.md` - Full documentation

That's it! Your notifications should now run automatically. 🎉
