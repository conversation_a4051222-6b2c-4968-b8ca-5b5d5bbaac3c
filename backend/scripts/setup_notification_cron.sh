#!/bin/bash

# Setup script for Local Notification Cron Job
# This script helps set up the cron job for automatic notifications

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path to the backend directory
BACKEND_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_PATH="$BACKEND_DIR/scripts/local_notification_cron.py"
LOG_FILE="/var/log/omi_notifications.log"

echo -e "${BLUE}=== OMI Notification Cron Job Setup ===${NC}"
echo -e "Backend directory: ${BACKEND_DIR}"
echo -e "Script path: ${SCRIPT_PATH}"
echo ""

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: python3 is not installed or not in PATH${NC}"
    exit 1
fi

# Check if the script exists
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}Error: Notification script not found at $SCRIPT_PATH${NC}"
    exit 1
fi

# Make the script executable
chmod +x "$SCRIPT_PATH"
echo -e "${GREEN}✓ Made notification script executable${NC}"

# Check if required Python packages are installed
echo -e "${YELLOW}Checking Python dependencies...${NC}"
cd "$BACKEND_DIR"

if ! python3 -c "import firebase_admin, pytz, asyncio" 2>/dev/null; then
    echo -e "${YELLOW}Installing required Python packages...${NC}"
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
    else
        pip3 install firebase-admin pytz
    fi
fi
echo -e "${GREEN}✓ Python dependencies are available${NC}"

# Create log file and set permissions
if sudo touch "$LOG_FILE" 2>/dev/null && sudo chmod 666 "$LOG_FILE" 2>/dev/null; then
    echo -e "${GREEN}✓ Log file created at $LOG_FILE${NC}"
else
    echo -e "${YELLOW}Warning: Could not create log file at $LOG_FILE${NC}"
    echo -e "${YELLOW}Using /tmp/omi_notifications.log instead${NC}"
    LOG_FILE="/tmp/omi_notifications.log"
    touch "$LOG_FILE"
    echo -e "${GREEN}✓ Log file created at $LOG_FILE${NC}"
fi

# Generate cron job entry
CRON_ENTRY="* * * * * cd $BACKEND_DIR && python3 scripts/local_notification_cron.py >> $LOG_FILE 2>&1"

echo ""
echo -e "${BLUE}=== Cron Job Configuration ===${NC}"
echo -e "The following cron job entry will run the notification system every minute:"
echo ""
echo -e "${YELLOW}$CRON_ENTRY${NC}"
echo ""

# Function to install cron job
install_cron() {
    echo -e "${YELLOW}Installing cron job...${NC}"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "local_notification_cron.py"; then
        echo -e "${YELLOW}Cron job already exists. Removing old entry...${NC}"
        crontab -l 2>/dev/null | grep -v "local_notification_cron.py" | crontab -
    fi
    
    # Add new cron job
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    echo -e "${GREEN}✓ Cron job installed successfully${NC}"
}

# Function to test the notification script
test_script() {
    echo -e "${YELLOW}Testing notification script...${NC}"
    cd "$BACKEND_DIR"
    
    if python3 scripts/local_notification_cron.py; then
        echo -e "${GREEN}✓ Notification script test completed${NC}"
    else
        echo -e "${RED}✗ Notification script test failed${NC}"
        echo -e "${YELLOW}Please check your Firebase configuration and environment variables${NC}"
        return 1
    fi
}

# Function to show status
show_status() {
    echo -e "${BLUE}=== Current Status ===${NC}"
    
    # Check if cron job exists
    if crontab -l 2>/dev/null | grep -q "local_notification_cron.py"; then
        echo -e "${GREEN}✓ Cron job is installed${NC}"
        echo -e "Current cron entry:"
        crontab -l 2>/dev/null | grep "local_notification_cron.py"
    else
        echo -e "${YELLOW}✗ Cron job is not installed${NC}"
    fi
    
    # Check log file
    if [ -f "$LOG_FILE" ]; then
        echo -e "${GREEN}✓ Log file exists at $LOG_FILE${NC}"
        if [ -s "$LOG_FILE" ]; then
            echo -e "Recent log entries:"
            tail -5 "$LOG_FILE"
        else
            echo -e "${YELLOW}Log file is empty${NC}"
        fi
    else
        echo -e "${YELLOW}✗ Log file not found${NC}"
    fi
}

# Function to remove cron job
remove_cron() {
    echo -e "${YELLOW}Removing cron job...${NC}"
    if crontab -l 2>/dev/null | grep -q "local_notification_cron.py"; then
        crontab -l 2>/dev/null | grep -v "local_notification_cron.py" | crontab -
        echo -e "${GREEN}✓ Cron job removed${NC}"
    else
        echo -e "${YELLOW}No cron job found to remove${NC}"
    fi
}

# Main menu
case "${1:-menu}" in
    "install")
        install_cron
        ;;
    "test")
        test_script
        ;;
    "status")
        show_status
        ;;
    "remove")
        remove_cron
        ;;
    "menu"|*)
        echo -e "${BLUE}Available commands:${NC}"
        echo -e "  ${GREEN}install${NC} - Install the cron job"
        echo -e "  ${GREEN}test${NC}    - Test the notification script"
        echo -e "  ${GREEN}status${NC}  - Show current status"
        echo -e "  ${GREEN}remove${NC}  - Remove the cron job"
        echo ""
        echo -e "Usage: $0 [install|test|status|remove]"
        echo ""
        echo -e "${YELLOW}Next steps:${NC}"
        echo -e "1. Set up your Firebase credentials (SERVICE_ACCOUNT_JSON environment variable)"
        echo -e "2. Run: $0 test"
        echo -e "3. Run: $0 install"
        echo -e "4. Monitor: tail -f $LOG_FILE"
        ;;
esac
