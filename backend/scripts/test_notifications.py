#!/usr/bin/env python3
"""
Test script for OMI Notification System

This script tests various components of the notification system to ensure
everything is working correctly before setting up the cron job or service.

Usage:
    python3 test_notifications.py [--dry-run]
"""

import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"ℹ No .env file found at {env_path}")
except ImportError:
    print("ℹ python-dotenv not available, using system environment variables")

import firebase_admin
import pytz

# Import notification components
try:
    from utils.other.notifications import should_run_job, start_cron_job
    from database.notifications import get_users_token_in_timezones
    from utils.notifications import send_notification
    print("✓ Successfully imported notification modules")
except ImportError as e:
    print(f"✗ Failed to import notification modules: {e}")
    sys.exit(1)


class NotificationTester:
    def __init__(self, dry_run=False):
        self.dry_run = dry_run
        self.firebase_initialized = False
        
    def test_firebase_initialization(self):
        """Test Firebase initialization"""
        print("\n=== Testing Firebase Initialization ===")
        
        try:
            # Check if Firebase is already initialized
            firebase_admin.get_app()
            print("✓ Firebase already initialized")
            self.firebase_initialized = True
            return True
        except ValueError:
            pass
        
        try:
            if os.environ.get('SERVICE_ACCOUNT_JSON'):
                service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
                credentials = firebase_admin.credentials.Certificate(service_account_info)
                firebase_admin.initialize_app(credentials)
                print("✓ Firebase initialized with SERVICE_ACCOUNT_JSON")
                self.firebase_initialized = True
                return True
            else:
                firebase_admin.initialize_app()
                print("✓ Firebase initialized with default credentials")
                self.firebase_initialized = True
                return True
        except Exception as e:
            print(f"✗ Firebase initialization failed: {e}")
            return False
    
    def test_timezone_logic(self):
        """Test timezone and scheduling logic"""
        print("\n=== Testing Timezone Logic ===")
        
        current_utc = datetime.now(pytz.utc)
        print(f"Current UTC time: {current_utc}")
        
        # Test should_run_job function
        should_run = should_run_job()
        print(f"Should run job now: {should_run}")
        
        # Find timezones at target hours
        target_hours = {8, 22}
        matching_timezones = []
        
        for tz_name in list(pytz.all_timezones)[:50]:  # Test first 50 timezones
            try:
                tz = pytz.timezone(tz_name)
                local_time = current_utc.astimezone(tz)
                if local_time.hour in target_hours:
                    matching_timezones.append(f"{tz_name} ({local_time.strftime('%H:%M')})")
            except Exception:
                continue
        
        if matching_timezones:
            print(f"✓ Found {len(matching_timezones)} timezones at target hours:")
            for tz in matching_timezones[:5]:  # Show first 5
                print(f"  - {tz}")
            if len(matching_timezones) > 5:
                print(f"  ... and {len(matching_timezones) - 5} more")
        else:
            print("ℹ No timezones currently at target hours (8 AM or 10 PM)")
        
        return True
    
    async def test_database_connection(self):
        """Test database connection and user queries"""
        print("\n=== Testing Database Connection ===")
        
        if not self.firebase_initialized:
            print("✗ Skipping database test - Firebase not initialized")
            return False
        
        try:
            # Test with a few sample timezones
            sample_timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo']
            users = await get_users_token_in_timezones(sample_timezones)
            print(f"✓ Database query successful. Found {len(users)} users in sample timezones")
            return True
        except Exception as e:
            print(f"✗ Database query failed: {e}")
            return False
    
    def test_notification_sending(self):
        """Test notification sending (dry run only)"""
        print("\n=== Testing Notification Sending ===")
        
        if not self.firebase_initialized:
            print("✗ Skipping notification test - Firebase not initialized")
            return False
        
        # This is always a dry run - we don't want to send actual notifications during testing
        print("ℹ This is a dry run - no actual notifications will be sent")
        
        try:
            # Test notification function exists and can be called
            # We use a dummy token that won't work, but tests the function
            dummy_token = "dummy_token_for_testing"
            title = "Test Notification"
            body = "This is a test notification from OMI system"
            
            print(f"✓ Notification function is available")
            print(f"  Title: {title}")
            print(f"  Body: {body}")
            print("ℹ Actual sending skipped in test mode")
            return True
        except Exception as e:
            print(f"✗ Notification function test failed: {e}")
            return False
    
    async def test_full_cron_logic(self):
        """Test the full cron job logic"""
        print("\n=== Testing Full Cron Logic ===")
        
        if not self.firebase_initialized:
            print("✗ Skipping cron logic test - Firebase not initialized")
            return False
        
        try:
            print("Running start_cron_job() function...")
            await start_cron_job()
            print("✓ Cron job logic completed successfully")
            return True
        except Exception as e:
            print(f"✗ Cron job logic failed: {e}")
            import traceback
            print(traceback.format_exc())
            return False
    
    def test_environment_variables(self):
        """Test required environment variables"""
        print("\n=== Testing Environment Variables ===")
        
        required_vars = []
        optional_vars = ['SERVICE_ACCOUNT_JSON', 'GOOGLE_APPLICATION_CREDENTIALS']
        
        # Check if at least one Firebase credential method is available
        firebase_creds_available = False
        for var in optional_vars:
            if os.environ.get(var):
                print(f"✓ {var} is set")
                firebase_creds_available = True
            else:
                print(f"ℹ {var} is not set")
        
        if firebase_creds_available:
            print("✓ Firebase credentials are available")
            return True
        else:
            print("⚠ No Firebase credentials found in environment variables")
            print("  Please set SERVICE_ACCOUNT_JSON or GOOGLE_APPLICATION_CREDENTIALS")
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 OMI Notification System Test Suite")
        print("=" * 50)
        
        tests = [
            ("Environment Variables", self.test_environment_variables),
            ("Firebase Initialization", self.test_firebase_initialization),
            ("Timezone Logic", self.test_timezone_logic),
            ("Database Connection", self.test_database_connection),
            ("Notification Sending", self.test_notification_sending),
            ("Full Cron Logic", self.test_full_cron_logic),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"✗ {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✓ PASS" if result else "✗ FAIL"
            print(f"{status:<8} {test_name}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Your notification system is ready.")
            return True
        else:
            print("⚠ Some tests failed. Please check the issues above.")
            return False


async def main():
    parser = argparse.ArgumentParser(description='Test OMI Notification System')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Run in dry-run mode (no actual notifications sent)')
    
    args = parser.parse_args()
    
    tester = NotificationTester(dry_run=args.dry_run)
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Set up cron job: ./scripts/setup_notification_cron.sh install")
        print("2. Or set up systemd service: ./scripts/setup_notification_service.sh install")
        print("3. Monitor logs to ensure notifications are working")
    else:
        print("\n🔧 Please fix the issues above before setting up the notification system.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
