# OMI Local Notification System Setup

This guide explains how to set up automated notifications for the OMI system running locally. The notification system sends two types of notifications based on user timezones:

1. **Morning Notifications (8:00 AM local time)**: "Wear your Memorion device to capture your conversations today."
2. **Evening Summary Notifications (10:00 PM local time)**: Daily conversation summaries with action plans

## Overview

The notification system includes:
- **Business Logic**: Defined in `utils/other/notifications.py`
- **Database Operations**: Handled by `database/notifications.py`
- **Firebase Integration**: For sending push notifications
- **Timezone Handling**: Supports all global timezones

## Setup Options

You have two options for running the notification system locally:

### Option 1: Cron Job (Simple)
Best for: Simple setups, minimal resource usage

### Option 2: Systemd Service (Recommended)
Best for: Production environments, better logging, automatic restart

## Prerequisites

1. **Python Environment**: Ensure all dependencies are installed
   ```bash
   cd /home/<USER>/omi/backend
   pip install -r requirements.txt
   ```

2. **Firebase Configuration**: Set up Firebase service account
   - Get your Firebase service account JSON from Firebase Console
   - Add it to your environment variables

## Option 1: Cron Job Setup

### Step 1: Configure Environment
```bash
# Copy environment template
cp .env.template .env

# Edit .env and add your Firebase service account JSON
nano .env
```

Add your Firebase service account JSON to the `SERVICE_ACCOUNT_JSON` variable:
```bash
SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"your-project-id",...}
```

### Step 2: Test the Script
```bash
# Make setup script executable
chmod +x scripts/setup_notification_cron.sh

# Test the notification script
./scripts/setup_notification_cron.sh test
```

### Step 3: Install Cron Job
```bash
# Install the cron job (runs every minute)
./scripts/setup_notification_cron.sh install

# Check status
./scripts/setup_notification_cron.sh status
```

### Step 4: Monitor Logs
```bash
# View logs
tail -f /var/log/omi_notifications.log
# or
tail -f /tmp/omi_notifications.log
```

### Cron Job Management
```bash
# Check status
./scripts/setup_notification_cron.sh status

# Remove cron job
./scripts/setup_notification_cron.sh remove
```

## Option 2: Systemd Service Setup (Recommended)

### Step 1: Configure Environment
Same as Option 1 - set up your `.env` file with Firebase credentials.

### Step 2: Test the Script
```bash
# Make setup script executable
chmod +x scripts/setup_notification_service.sh

# Test the notification script
./scripts/setup_notification_service.sh test
```

### Step 3: Install and Start Service
```bash
# Install the systemd service
./scripts/setup_notification_service.sh install

# Start the service
./scripts/setup_notification_service.sh start

# Check status
./scripts/setup_notification_service.sh status
```

### Step 4: Monitor Service
```bash
# View real-time logs
./scripts/setup_notification_service.sh logs

# Check service status
./scripts/setup_notification_service.sh status
```

### Service Management
```bash
# Start service
./scripts/setup_notification_service.sh start

# Stop service
./scripts/setup_notification_service.sh stop

# Restart service
./scripts/setup_notification_service.sh restart

# Remove service
./scripts/setup_notification_service.sh remove
```

## How It Works

### Notification Schedule
The system checks every minute if any timezone is at:
- **8:00 AM**: Sends morning reminder notifications
- **10:00 PM**: Sends daily summary notifications

### User Targeting
- Queries Firestore for users in timezones matching target times
- Sends notifications only to users with valid FCM tokens
- Handles timezone calculations automatically

### Notification Types

#### Morning Notifications (8:00 AM)
- **Title**: "Memorion"
- **Body**: "Wear your Memorion device to capture your conversations today."
- **Target**: All users at 8:00 AM local time

#### Evening Summary Notifications (10:00 PM)
- **Title**: "Here is your action plan for tomorrow"
- **Body**: AI-generated summary of the day's conversations
- **Target**: Users with conversations from that day at 10:00 PM local time

## Troubleshooting

### Common Issues

1. **Firebase Authentication Error**
   ```bash
   # Check if SERVICE_ACCOUNT_JSON is properly set
   echo $SERVICE_ACCOUNT_JSON
   
   # Verify Firebase credentials
   python3 -c "import firebase_admin; firebase_admin.initialize_app()"
   ```

2. **Import Errors**
   ```bash
   # Ensure you're in the backend directory
   cd /home/<USER>/omi/backend
   
   # Check Python path
   python3 -c "import sys; print(sys.path)"
   ```

3. **Permission Issues**
   ```bash
   # For cron job logs
   sudo chmod 666 /var/log/omi_notifications.log
   
   # For systemd service
   sudo systemctl status omi-notifications
   ```

### Debugging

1. **Test Notification Script Manually**
   ```bash
   cd /home/<USER>/omi/backend
   python3 scripts/local_notification_cron.py
   ```

2. **Check Logs**
   ```bash
   # Cron job logs
   tail -f /var/log/omi_notifications.log
   
   # Systemd service logs
   journalctl -u omi-notifications -f
   ```

3. **Verify Database Connection**
   ```bash
   python3 -c "
   import sys
   sys.path.append('.')
   from database.notifications import get_users_token_in_timezones
   print('Database connection OK')
   "
   ```

## Environment Variables

Required environment variables in `.env`:

```bash
# Firebase (required)
SERVICE_ACCOUNT_JSON={"type":"service_account",...}

# Optional
LOG_LEVEL=INFO
NOTIFICATION_DRY_RUN=false
```

## Files Created

- `scripts/local_notification_cron.py` - Cron job script
- `scripts/notification_service.py` - Systemd service daemon
- `scripts/setup_notification_cron.sh` - Cron job setup script
- `scripts/setup_notification_service.sh` - Systemd service setup script
- `scripts/omi-notifications.service` - Systemd service definition

## Security Notes

- Keep your Firebase service account JSON secure
- Use environment variables for sensitive data
- Monitor logs for any authentication issues
- Regularly rotate Firebase service account keys

## Support

If you encounter issues:
1. Check the logs for error messages
2. Verify Firebase credentials
3. Ensure all Python dependencies are installed
4. Test the notification script manually first
